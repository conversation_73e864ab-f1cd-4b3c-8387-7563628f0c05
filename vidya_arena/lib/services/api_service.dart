import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../models/api_response.dart';
import 'token_service.dart';

class ApiService {
  static const String _baseUrl = kDebugMode 
      ? 'http://localhost:3000/api' 
      : 'https://your-production-api.com/api';
  
  static const Duration _timeout = Duration(seconds: 30);
  
  final TokenService _tokenService = TokenService();

  // Helper method to get headers
  Future<Map<String, String>> _getHeaders({bool includeAuth = false}) async {
    Map<String, String> headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (includeAuth) {
      String? token = await _tokenService.getToken();
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    return headers;
  }

  // Helper method to handle HTTP responses
  ApiResponse<T> _handleResponse<T>(http.Response response, T Function(Map<String, dynamic>) fromJson) {
    try {
      if (kDebugMode) {
        print('Response status code: ${response.statusCode}');
        print('Response headers: ${response.headers}');
        print('Response body: ${response.body}');
      }

      final Map<String, dynamic> data = json.decode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ApiResponse<T>(
          success: data['success'] ?? true,
          message: data['message'] ?? 'Success',
          data: data['data'] != null ? fromJson(data['data']) : null,
        );
      } else {
        return ApiResponse<T>(
          success: false,
          message: data['message'] ?? 'An error occurred',
          errors: data['errors'],
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing response: $e');
        print('Response status code: ${response.statusCode}');
        print('Response headers: ${response.headers}');
        print('Response body: ${response.body}');
        print('Response body type: ${response.body.runtimeType}');
      }
      return ApiResponse<T>(
        success: false,
        message: 'Failed to parse server response: ${response.body}',
      );
    }
  }

  // Register user
  Future<ApiResponse<UserData>> registerUser(User user) async {
    try {
      final headers = await _getHeaders();
      final body = json.encode({
        'name': user.name,
        'email': user.email,
        'password': user.password,
        'mobile': user.mobile,
        'dob': user.dob,
        'gender': user.gender,
      });

      if (kDebugMode) {
        print('Making registration request to: $_baseUrl/users/register');
        print('Request headers: $headers');
        print('Request body: $body');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/users/register'),
        headers: headers,
        body: body,
      ).timeout(_timeout);

      final apiResponse = _handleResponse<UserData>(
        response, 
        (data) => UserData.fromJson(data)
      );

      // Save token if registration successful
      if (apiResponse.success && apiResponse.data?.token != null) {
        await _tokenService.saveToken(apiResponse.data!.token!);
      }

      return apiResponse;

    } catch (e) {
      if (kDebugMode) {
        print('Registration error: $e');
      }
      return ApiResponse<UserData>(
        success: false,
        message: 'Network error. Please check your connection.',
      );
    }
  }

  // Login user
  Future<ApiResponse<UserData>> loginUser(String email, String password) async {
    try {
      final headers = await _getHeaders();
      final body = json.encode({
        'email': email,
        'password': password,
      });

      if (kDebugMode) {
        print('Making login request to: $_baseUrl/users/login');
        print('Request headers: $headers');
        print('Request body: $body');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/users/login'),
        headers: headers,
        body: body,
      ).timeout(_timeout);

      final apiResponse = _handleResponse<UserData>(
        response, 
        (data) => UserData.fromJson(data)
      );

      // Save token if login successful
      if (apiResponse.success && apiResponse.data?.token != null) {
        await _tokenService.saveToken(apiResponse.data!.token!);
      }

      return apiResponse;

    } catch (e) {
      if (kDebugMode) {
        print('Login error: $e');
      }
      return ApiResponse<UserData>(
        success: false,
        message: 'Network error. Please check your connection.',
      );
    }
  }

  // Get user profile
  Future<ApiResponse<User>> getUserProfile() async {
    try {
      final headers = await _getHeaders(includeAuth: true);

      final response = await http.get(
        Uri.parse('$_baseUrl/users/profile'),
        headers: headers,
      ).timeout(_timeout);

      return _handleResponse<User>(
        response, 
        (data) => User.fromMap(data['user'])
      );

    } catch (e) {
      if (kDebugMode) {
        print('Get profile error: $e');
      }
      return ApiResponse<User>(
        success: false,
        message: 'Network error. Please check your connection.',
      );
    }
  }

  // Check if email exists
  Future<ApiResponse<bool>> checkEmailExists(String email) async {
    try {
      final headers = await _getHeaders();

      final response = await http.get(
        Uri.parse('$_baseUrl/users/check-email?email=${Uri.encodeComponent(email)}'),
        headers: headers,
      ).timeout(_timeout);

      return _handleResponse<bool>(
        response, 
        (data) => data['exists'] as bool
      );

    } catch (e) {
      if (kDebugMode) {
        print('Check email error: $e');
      }
      return ApiResponse<bool>(
        success: false,
        message: 'Network error. Please check your connection.',
      );
    }
  }

  // Logout user
  Future<void> logout() async {
    await _tokenService.deleteToken();
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    String? token = await _tokenService.getToken();
    return token != null;
  }
}
